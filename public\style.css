* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #FFA673 0%, #FF8A50 50%, #FF6B35 100%);
    min-height: 100vh;
    padding: 0;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: transparent;
    padding-bottom: 100px;
}

.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    color: #2D3748;
    padding: 30px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-bottom: 50px;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 166, 115, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

.header-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #FFA673, #FF8A50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(255, 166, 115, 0.4);
    position: relative;
    z-index: 2;
    animation: bounce 2s ease-in-out infinite;
}

.header-icon img {
    width: 100%;
    height: 45px;
    filter: none;
}

.header h1 {
    font-size: 2.2rem;
    margin-bottom: 8px;
    font-weight: 700;
    background: linear-gradient(135deg, #FFA673, #FF6B35);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 2;
    line-height: 1.2;
}

.header p {
    font-size: 1rem;
    color: #718096;
    font-weight: 400;
    position: relative;
    z-index: 2;
    margin-bottom: 0;
}

.form-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 40px 30px;
    max-width: 480px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px 25px 0 0;
    position: relative;
    box-shadow: 0 -8px 30px rgba(0, 0, 0, 0.1);
    border-radius: 30px;
}

.form-container::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 6px;
    background: #FFA673;
    border-radius: 3px;
    opacity: 0.3;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2D3748;
    font-size: 1rem;
}

.form-group label i {
    margin-right: 12px;
    color: #FFA673;
    font-size: 1.2rem;
    width: 20px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 15px 18px;
    border: 2px solid #E2E8F0;
    border-radius: 15px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    font-weight: 500;
    color: #2D3748;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #FFA673;
    box-shadow: 0 0 0 4px rgba(255, 166, 115, 0.2);
    transform: translateY(-2px);
}

.btn-submit {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #FFA673 0%, #FF8A50 100%);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(255, 166, 115, 0.4);
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-submit:hover::before {
    left: 100%;
}

.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(255, 166, 115, 0.5);
}

.btn-submit:active {
    transform: translateY(-1px);
}

.btn-submit i {
    margin-right: 12px;
    font-size: 1.3rem;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 166, 115, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(255, 166, 115, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 166, 115, 0); }
}

.form-group {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.btn-submit {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
    animation-delay: 0.4s;
}

.alert {
    padding: 20px 25px;
    margin: 20px 40px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    animation: slideInUp 0.5s ease-out;
}

.alert i {
    margin-right: 15px;
    font-size: 1.4rem;
}

.alert-error {
    background: rgba(255, 107, 53, 0.1);
    color: #FF6B35;
    border: 2px solid rgba(255, 107, 53, 0.3);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
}

.action-buttons {
    padding: 20px 30px;
    display: flex;
    gap: 15px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
}

.btn {
    padding: 12px 20px;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 0.95rem;
}

.btn i {
    margin-right: 10px;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #FFA673, #FF8A50);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 166, 115, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(255, 166, 115, 0.5);
}

.btn-secondary {
    background: rgba(45, 55, 72, 0.1);
    color: #2D3748;
    border: 2px solid rgba(45, 55, 72, 0.2);
}

.btn-secondary:hover {
    background: rgba(45, 55, 72, 0.2);
    transform: translateY(-2px);
}

.pdf-container {
    flex: 1;
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    height: 100%;
    margin-top: 20px;
    margin-left: 100px;
    margin-right: 100px;
}

.pdf-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 166, 115, 0.1);
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.btn-control {
    padding: 12px 16px;
    background: linear-gradient(135deg, #FFA673, #FF8A50);
    color: white;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 5px 15px rgba(255, 166, 115, 0.3);
    font-size: 1rem;
}

.btn-control:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 166, 115, 0.4);
}

#page-info {
    font-weight: 700;
    color: #2D3748;
    font-size: 1.1rem;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
}

.pdf-viewer {
    text-align: center;
    background: rgba(255, 255, 255, 0.5);
    padding: 30px;
    border-radius: 25px;
    overflow: auto;
    max-height: 70vh;
    backdrop-filter: blur(10px);
}

#pdf-canvas {
    border: none;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

#pdf-canvas:hover {
    transform: scale(1.02);
}

.loading {
    text-align: center;
    padding: 60px;
    color: #FFA673;
    font-size: 1.3rem;
    font-weight: 600;
}

.loading i {
    margin-right: 15px;
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.response-data {
    margin: 30px 40px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    border: 2px solid rgba(255, 166, 115, 0.2);
    backdrop-filter: blur(10px);
}

.response-data h3 {
    margin-bottom: 20px;
    color: #2D3748;
    font-weight: 700;
}

.response-data pre {
    background: rgba(255, 255, 255, 0.8);
    padding: 20px;
    border-radius: 15px;
    border: 1px solid rgba(255, 166, 115, 0.3);
    overflow-x: auto;
    font-size: 0.95rem;
    color: #4A5568;
    font-family: 'Fira Code', 'Consolas', monospace;
}

/* Floating elements for decoration */
.floating-shape {
    position: fixed;
    pointer-events: none;
    z-index: -1;
}

.shape-1 {
    top: 10%;
    left: 10%;
    width: 100px;
    height: 100px;
    background: rgba(255, 166, 115, 0.1);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
}

.shape-2 {
    top: 60%;
    right: 15%;
    width: 80px;
    height: 80px;
    background: rgba(255, 138, 80, 0.1);
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    animation: float 6s ease-in-out infinite reverse;
}

.shape-3 {
    bottom: 20%;
    left: 20%;
    width: 60px;
    height: 60px;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 50%;
    animation: float 10s ease-in-out infinite;
}

@media (max-width: 768px) {
    body {
        padding: 0;
    }

    .header {
        padding: 25px 15px;
        border-radius: 0;
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .header p {
        font-size: 0.9rem;
    }

    .header-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 15px;
    }

    .header-icon img {
        width: 28px;
        height: 28px;
    }

    .form-container {
        padding: 30px 20px;
        border-radius: 20px 20px 0 0;
        margin: 0;
        max-width: 100%;
    }

    .action-buttons {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
    }

    .pdf-controls {
        flex-wrap: wrap;
        gap: 15px;
        padding: 15px;
    }

    .alert {
        margin: 15px 20px;
    }

    .pdf-container {
        padding: 20px;
        border-radius: 25px 25px 0 0;
    }

    .floating-shape {
        display: none;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 20px 15px;
    }

    .header h1 {
        font-size: 1.6rem;
    }

    .header p {
        font-size: 0.85rem;
    }

    .header-icon {
        width: 45px;
        height: 45px;
        margin-bottom: 12px;
    }

    .header-icon img {
        width: 25px;
        height: 25px;
    }

    .form-container {
        padding: 25px 15px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group input,
    .form-group select {
        padding: 12px 15px;
        font-size: 0.95rem;
    }

    .btn-submit {
        padding: 14px;
        font-size: 1rem;
        margin-top: 20px;
    }
}
