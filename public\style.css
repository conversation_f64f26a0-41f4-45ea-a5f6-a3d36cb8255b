* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 40px;
    text-align: center;
}

.header i {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 300;
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.form-container {
    padding: 40px;
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.form-group label i {
    margin-right: 8px;
    color: #4facfe;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

.btn-submit {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
}

.btn-submit i {
    margin-right: 10px;
}

.info-box {
    background: #f8f9fa;
    padding: 30px 40px;
    border-top: 1px solid #e1e5e9;
}

.info-box h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.info-box h3 i {
    margin-right: 10px;
    color: #4facfe;
}

.info-box ul {
    list-style: none;
}

.info-box li {
    padding: 8px 0;
    color: #666;
    position: relative;
    padding-left: 25px;
}

.info-box li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #4facfe;
    font-weight: bold;
}

.alert {
    padding: 15px 20px;
    margin: 20px 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
}

.alert i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.alert-error {
    background: #fee;
    color: #c33;
    border: 1px solid #fcc;
}

.action-buttons {
    padding: 20px 40px;
    display: flex;
    gap: 15px;
    border-bottom: 1px solid #e1e5e9;
}

.btn {
    padding: 12px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background: #4facfe;
    color: white;
}

.btn-primary:hover {
    background: #3d8bfe;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.pdf-container {
    padding: 20px;
}

.pdf-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.btn-control {
    padding: 8px 12px;
    background: #4facfe;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-control:hover {
    background: #3d8bfe;
}

#page-info {
    font-weight: 600;
    color: #333;
}

.pdf-viewer {
    text-align: center;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    overflow: auto;
    max-height: 80vh;
}

#pdf-canvas {
    border: 1px solid #ddd;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    max-width: 100%;
    height: auto;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 1.1rem;
}

.loading i {
    margin-right: 10px;
    font-size: 1.5rem;
}

.response-data {
    margin: 20px 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
}

.response-data h3 {
    margin-bottom: 15px;
    color: #333;
}

.response-data pre {
    background: #fff;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
    overflow-x: auto;
    font-size: 0.9rem;
    color: #666;
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 10px;
    }
    
    .header {
        padding: 30px 20px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .form-container {
        padding: 30px 20px;
    }
    
    .info-box {
        padding: 20px;
    }
    
    .action-buttons {
        padding: 15px 20px;
        flex-direction: column;
    }
    
    .pdf-controls {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .alert {
        margin: 15px 20px;
    }
}
