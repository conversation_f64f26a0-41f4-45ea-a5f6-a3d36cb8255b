<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link rel="stylesheet" href="/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
</head>
<body>
    <!-- Floating decorative shapes -->
    <div class="floating-shape shape-1"></div>
    <div class="floating-shape shape-2"></div>
    <div class="floating-shape shape-3"></div>

    <div class="container">
        <div class="content-wrapper">
            <div class="header">
                <div class="header-icon">
                    <i class="fas fa-file-pdf" style="color: white; font-size: 1.8rem;"></i>
                </div>
                <h1><%= title %></h1>
                <p>NIM: <%= nim %> | Tahun: <%= year %> | Semester: <%= season %></p>
            </div>

        <div class="action-buttons">
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                Kembali
            </a>
            <% if (pdfData) { %>
                <button onclick="downloadPDF()" class="btn btn-primary">
                    <i class="fas fa-download"></i>
                    Download PDF
                </button>
            <% } %>
        </div>

        <% if (error) { %>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <%= error %>
            </div>
            <% if (typeof responseData !== 'undefined') { %>
                <div class="response-data">
                    <h3>Response dari server:</h3>
                    <pre><%= JSON.stringify(responseData, null, 2) %></pre>
                </div>
            <% } %>
        <% } %>

        <% if (pdfData) { %>
            <div class="pdf-container">
                <div class="pdf-controls">
                    <button onclick="prevPage()" class="btn-control">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="page-info">
                        Halaman <span id="page-num">1</span> dari <span id="page-count">-</span>
                    </span>
                    <button onclick="nextPage()" class="btn-control">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <button onclick="zoomIn()" class="btn-control">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button onclick="zoomOut()" class="btn-control">
                        <i class="fas fa-search-minus"></i>
                    </button>
                </div>
                
                <div class="pdf-viewer">
                    <canvas id="pdf-canvas"></canvas>
                </div>
                
                <div class="loading" id="loading">
                    <i class="fas fa-spinner fa-spin"></i>
                    Memuat PDF...
                </div>
            </div>

             <center>
            <p style="font-size: 0.8rem; color: #ffffff; margin-top: 20px;">By Freack & Gum</p>
        </center>

            <script>
                // PDF data dari server
                const pdfData = '<%= pdfData %>';
                
                let pdfDoc = null;
                let pageNum = 1;
                let pageRendering = false;
                let pageNumPending = null;
                let scale = 1.5;
                const canvas = document.getElementById('pdf-canvas');
                const ctx = canvas.getContext('2d');

                // Load PDF
                function loadPDF() {
                    const loadingTask = pdfjsLib.getDocument({data: atob(pdfData)});
                    loadingTask.promise.then(function(pdf) {
                        pdfDoc = pdf;
                        document.getElementById('page-count').textContent = pdf.numPages;
                        document.getElementById('loading').style.display = 'none';
                        renderPage(pageNum);
                    }).catch(function(error) {
                        console.error('Error loading PDF:', error);
                        document.getElementById('loading').innerHTML = 
                            '<i class="fas fa-exclamation-triangle"></i> Error loading PDF';
                    });
                }

                // Render page
                function renderPage(num) {
                    pageRendering = true;
                    pdfDoc.getPage(num).then(function(page) {
                        const viewport = page.getViewport({scale: scale});
                        canvas.height = viewport.height;
                        canvas.width = viewport.width;

                        const renderContext = {
                            canvasContext: ctx,
                            viewport: viewport
                        };
                        
                        const renderTask = page.render(renderContext);
                        renderTask.promise.then(function() {
                            pageRendering = false;
                            if (pageNumPending !== null) {
                                renderPage(pageNumPending);
                                pageNumPending = null;
                            }
                        });
                    });

                    document.getElementById('page-num').textContent = num;
                }

                // Queue render page
                function queueRenderPage(num) {
                    if (pageRendering) {
                        pageNumPending = num;
                    } else {
                        renderPage(num);
                    }
                }

                // Navigation functions
                function prevPage() {
                    if (pageNum <= 1) return;
                    pageNum--;
                    queueRenderPage(pageNum);
                }

                function nextPage() {
                    if (pageNum >= pdfDoc.numPages) return;
                    pageNum++;
                    queueRenderPage(pageNum);
                }

                function zoomIn() {
                    scale += 0.25;
                    queueRenderPage(pageNum);
                }

                function zoomOut() {
                    if (scale <= 0.5) return;
                    scale -= 0.25;
                    queueRenderPage(pageNum);
                }

                function downloadPDF() {
                    const link = document.createElement('a');
                    link.href = 'data:application/pdf;base64,' + pdfData;
                    link.download = 'KHS_<%= nim %>_<%= year %>_<%= season %>.pdf';
                    link.click();
                }

                // Initialize PDF when page loads
                document.addEventListener('DOMContentLoaded', loadPDF);
            </script>
        <% } %>
        </div>
    </div>
</body>
</html>
