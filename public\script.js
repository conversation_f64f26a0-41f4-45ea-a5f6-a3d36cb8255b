// Client-side JavaScript for form validation and interactions

document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const nimInput = document.getElementById('nim');
    const yearSelect = document.getElementById('year');
    const seasonSelect = document.getElementById('season');
    const submitBtn = document.querySelector('.btn-submit');

    // Set current year as default
    if (yearSelect) {
        const currentYear = new Date().getFullYear();
        yearSelect.value = currentYear.toString();
    }

    // NIM input validation
    if (nimInput) {
        nimInput.addEventListener('input', function(e) {
            // Remove non-numeric characters
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
            
            // Validate length (typical NIM is 10 digits)
            if (e.target.value.length > 0 && e.target.value.length < 8) {
                e.target.setCustomValidity('NIM minimal 8 digit');
            } else {
                e.target.setCustomValidity('');
            }
        });
    }

    // Form submission handling
    if (form) {
        form.addEventListener('submit', function(e) {
            const nim = nimInput.value.trim();
            const year = yearSelect.value;
            const season = seasonSelect.value;

            // Validate all fields
            if (!nim || !year || !season) {
                e.preventDefault();
                showAlert('Semua field harus diisi!', 'error');
                return;
            }

            // Validate NIM format
            if (nim.length < 8 || !/^\d+$/.test(nim)) {
                e.preventDefault();
                showAlert('NIM harus berupa angka minimal 8 digit!', 'error');
                return;
            }

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mengambil data...';
            submitBtn.disabled = true;
        });
    }

    // Show alert function
    function showAlert(message, type) {
        // Remove existing alerts
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
        `;

        // Insert after header
        const header = document.querySelector('.header');
        header.insertAdjacentElement('afterend', alert);

        // Auto remove after 5 seconds
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }

    // Add smooth scrolling for better UX
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add keyboard shortcuts for PDF viewer (if on result page)
    if (window.location.pathname.includes('result') || document.getElementById('pdf-canvas')) {
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowLeft':
                    if (typeof prevPage === 'function') prevPage();
                    break;
                case 'ArrowRight':
                    if (typeof nextPage === 'function') nextPage();
                    break;
                case '+':
                case '=':
                    if (typeof zoomIn === 'function') zoomIn();
                    break;
                case '-':
                    if (typeof zoomOut === 'function') zoomOut();
                    break;
            }
        });
    }
});

// Utility functions
function formatNIM(input) {
    // Format NIM with spaces for better readability
    let value = input.value.replace(/\s/g, '');
    let formattedValue = value.replace(/(\d{4})(\d{3})(\d{3})/g, '$1 $2 $3');
    input.value = formattedValue;
}

function validateForm() {
    const nim = document.getElementById('nim').value.trim();
    const year = document.getElementById('year').value;
    const season = document.getElementById('season').value;

    return nim && year && season && nim.length >= 8;
}

// Add loading animation
function showLoading() {
    const loading = document.createElement('div');
    loading.id = 'page-loading';
    loading.innerHTML = `
        <div style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            font-size: 1.2rem;
            color: #4facfe;
        ">
            <i class="fas fa-spinner fa-spin" style="margin-right: 10px;"></i>
            Memproses permintaan...
        </div>
    `;
    document.body.appendChild(loading);
}

function hideLoading() {
    const loading = document.getElementById('page-loading');
    if (loading) {
        loading.remove();
    }
}
