* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

/* Prevent layout shift */
img {
    max-width: 100%;
    height: auto;
}

/* Prevent FOUC (Flash of Unstyled Content) */
.container {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
}

body {
    font-family: 'Poppins', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #FFA673 0%, #FF8A50 50%, #FF6B35 100%);
    min-height: 100vh;
    padding: 0;
    overflow-x: hidden;
    position: relative;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: transparent;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    padding: 0 20px;
}

.header {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    color: #2D3748;
    padding: 40px 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-radius: 25px;
    margin: 20px 0;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 166, 115, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

.header-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #FFA673, #FF8A50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(255, 166, 115, 0.4);
    position: relative;
    z-index: 2;
    animation: bounce 2s ease-in-out infinite;
    cursor: pointer;
    transition: all 0.3s ease;
}

.header-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(255, 166, 115, 0.5);
}

.header-icon img {
    width: 35px;
    height: 35px;
    filter: none;
    object-fit: contain;
    display: block;
}

.header h1 {
    font-size: 2.4rem;
    margin-bottom: 10px;
    font-weight: 700;
    background: linear-gradient(135deg, #FFA673, #FF6B35);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    z-index: 2;
    line-height: 1.2;
    letter-spacing: -0.02em;
}

.header p {
    font-size: 1.1rem;
    color: #718096;
    font-weight: 500;
    position: relative;
    z-index: 2;
    margin-bottom: 0;
    opacity: 0.9;
}

.form-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 40px 30px;
    max-width: 500px;
    margin: 0 auto 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    position: relative;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.form-container::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: #FFA673;
    border-radius: 2px;
    opacity: 0.5;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2D3748;
    font-size: 1rem;
}

.form-group label i {
    margin-right: 12px;
    color: #FFA673;
    font-size: 1.2rem;
    width: 20px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 15px 18px;
    border: 2px solid #E2E8F0;
    border-radius: 15px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    font-weight: 500;
    color: #2D3748;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #FFA673;
    box-shadow: 0 0 0 4px rgba(255, 166, 115, 0.2);
    transform: translateY(-2px);
}

.form-group input:disabled,
.form-group select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-submit {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #FFA673 0%, #FF8A50 100%);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(255, 166, 115, 0.4);
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-submit:hover::before {
    left: 100%;
}

.btn-submit:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 15px 40px rgba(255, 166, 115, 0.5);
}

.btn-submit:active {
    transform: translateY(-1px);
}

.btn-submit:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-submit i {
    margin-right: 12px;
    font-size: 1.3rem;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.form-group {
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.form-group:nth-child(1) { animation-delay: 0.1s; }
.form-group:nth-child(2) { animation-delay: 0.2s; }
.form-group:nth-child(3) { animation-delay: 0.3s; }
.btn-submit { 
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;
    animation-delay: 0.4s; 
}

.alert {
    padding: 20px 25px;
    margin: 0 0 20px 0;
    border-radius: 20px;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    animation: slideInUp 0.5s ease-out;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.alert i {
    margin-right: 15px;
    font-size: 1.4rem;
}

.alert-error {
    background: rgba(255, 107, 53, 0.1);
    color: #FF6B35;
    border: 1px solid rgba(255, 107, 53, 0.3);
    box-shadow: 0 10px 30px rgba(255, 107, 53, 0.2);
}

.action-buttons {
    padding: 20px 30px;
    display: flex;
    gap: 15px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    margin: 0 0 20px 0;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn {
    padding: 12px 20px;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 0.95rem;
}

.btn i {
    margin-right: 10px;
    font-size: 1.1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #FFA673, #FF8A50);
    color: white;
    box-shadow: 0 8px 25px rgba(255, 166, 115, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(255, 166, 115, 0.5);
}

.btn-secondary {
    background: rgba(45, 55, 72, 0.1);
    color: #2D3748;
    border: 2px solid rgba(45, 55, 72, 0.2);
}

.btn-secondary:hover {
    background: rgba(45, 55, 72, 0.2);
    transform: translateY(-2px);
}

.pdf-container {
    flex: 1;
    padding: 30px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.pdf-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 166, 115, 0.1);
    border-radius: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 166, 115, 0.2);
    box-shadow: 0 4px 16px rgba(255, 166, 115, 0.15);
}

.btn-control {
    padding: 12px 16px;
    background: linear-gradient(135deg, #FFA673, #FF8A50);
    color: white;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 5px 15px rgba(255, 166, 115, 0.3);
    font-size: 1rem;
}

.btn-control:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 166, 115, 0.4);
}

.btn-control:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#page-info {
    font-weight: 700;
    color: #2D3748;
    font-size: 1.1rem;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
}

.pdf-viewer {
    text-align: center;
    background: rgba(255, 255, 255, 0.5);
    padding: 30px;
    border-radius: 25px;
    overflow: auto;
    max-height: 70vh;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

#pdf-canvas {
    border: none;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

#pdf-canvas:hover {
    transform: scale(1.02);
}

.loading {
    text-align: center;
    padding: 60px;
    color: #FFA673;
    font-size: 1.3rem;
    font-weight: 600;
}

.loading i {
    margin-right: 15px;
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

/* Content wrapper for better spacing */
.content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-height: calc(100vh - 40px);
    padding: 20px 0;
}

/* Floating elements for decoration */
.floating-shape {
    position: fixed;
    pointer-events: none;
    z-index: -1;
}

.shape-1 {
    top: 10%;
    left: 10%;
    width: 100px;
    height: 100px;
    background: rgba(255, 166, 115, 0.1);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
}

.shape-2 {
    top: 60%;
    right: 15%;
    width: 80px;
    height: 80px;
    background: rgba(255, 138, 80, 0.1);
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    animation: float 6s ease-in-out infinite reverse;
}

.shape-3 {
    bottom: 20%;
    left: 20%;
    width: 60px;
    height: 60px;
    background: rgba(255, 107, 53, 0.1);
    border-radius: 50%;
    animation: float 10s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .header {
        padding: 30px 20px;
        border-radius: 20px;
        margin: 15px 0;
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .header p {
        font-size: 0.9rem;
    }

    .header-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 15px;
    }

    .header-icon img {
        width: 28px;
        height: 28px;
    }

    .form-container {
        padding: 30px 20px;
        border-radius: 20px;
        margin: 0 0 15px;
        max-width: 100%;
    }

    .action-buttons {
        padding: 15px;
        flex-direction: column;
        gap: 12px;
        border-radius: 15px;
        margin: 0 0 15px;
    }

    .pdf-controls {
        flex-wrap: wrap;
        gap: 12px;
        padding: 15px;
        border-radius: 20px;
    }

    .alert {
        margin: 15px 0;
        border-radius: 15px;
    }

    .pdf-container {
        padding: 20px;
        border-radius: 20px;
        margin-bottom: 15px;
    }

    .floating-shape {
        display: none;
    }

    .content-wrapper {
        gap: 15px;
        padding: 15px 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .header {
        padding: 25px 15px;
        border-radius: 15px;
        margin: 10px 0;
    }

    .header h1 {
        font-size: 1.6rem;
    }

    .header p {
        font-size: 0.85rem;
    }

    .header-icon {
        width: 45px;
        height: 45px;
        margin-bottom: 12px;
    }

    .header-icon img {
        width: 25px;
        height: 25px;
    }

    .form-container {
        padding: 25px 15px;
        border-radius: 15px;
        margin: 0 0 10px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group input,
    .form-group select {
        padding: 12px 15px;
        font-size: 0.95rem;
        border-radius: 12px;
    }

    .btn-submit {
        padding: 14px;
        font-size: 1rem;
        margin-top: 20px;
        border-radius: 15px;
    }

    .action-buttons {
        padding: 12px;
        border-radius: 12px;
        margin: 0 0 10px;
    }

    .pdf-container {
        padding: 15px;
        border-radius: 15px;
        margin-bottom: 10px;
    }

    .btn {
        padding: 10px 15px;
        font-size: 0.9rem;
        border-radius: 12px;
    }
}
